<template>
  <view class="commercial-house-list">
    <!-- 顶部搜索栏 -->
    <view class="search-header bg-white p-20rpx flex items-center">
      <view class="city-selector flex items-center mr-20rpx">
        <text class="text-32rpx font-bold">北京</text>
        <text class="i-carbon-chevron-down text-24rpx ml-6rpx"></text>
      </view>
      <view
        class="search-box flex-1 flex items-center bg-gray-100 rounded-full px-30rpx py-16rpx"
        @tap="navigateToSearch"
      >
        <text class="i-carbon-search text-28rpx color-grey mr-10rpx"></text>
        <text class="color-grey text-28rpx">搜索区域/地址/楼盘名</text>
      </view>
      <view class="map-icon ml-20rpx p-10rpx" @tap="navigateToMap">
        <text class="i-carbon-map text-36rpx"></text>
      </view>
    </view>
    <!-- 房源列表 -->
    <z-paging
      ref="paging"
      v-model="houseList"
      @query="queryHouseList"
      :refresher-enabled="true"
      :show-refresher-update-time="true"
      :auto-scroll-to-top-when-reload="true"
      :empty-view-text="'暂无符合条件的商业地产'"
    >
      <!-- 筛选条件栏 -->
      <HouseFilterBar
        :showHouseType="false"
        :showPropertyType="true"
        :areaOptions="areaOptions"
        :priceOptions="priceOptions"
        :propertyTypeOptions="propertyTypeOptions"
        :sortOptions="sortOptions"
        :initialFilters="activeFilters"
        :priceText="'价格'"
        :propertyTypeText="'类型'"
        @filter-change="handleFilterChange"
      />

      <!-- 商业地产特色功能区 -->
      <view
        class="commercial-features bg-white mx-20rpx mb-20rpx rounded-xl shadow-sm"
      >
        <view class="px-30rpx py-30rpx">
          <text class="text-30rpx font-bold mb-30rpx block">商业地产</text>
          <view class="grid grid-cols-4 gap-20rpx">
            <view
              v-for="(item, index) in commercialFeatures"
              :key="index"
              class="feature-card bg-gradient-to-br rounded-xl p-20rpx"
              :class="item.bgClass"
              @tap="applyFilter(item.filter)"
            >
              <view class="feature-icon mb-12rpx">
                <text :class="[item.icon, 'text-white text-32rpx']"></text>
              </view>
              <text class="text-24rpx text-white font-bold">{{
                item.name
              }}</text>
              <text
                class="text-20rpx text-white text-opacity-80 block mt-4rpx"
                >{{ item.desc }}</text
              >
            </view>
          </view>
        </view>
      </view>

      <!-- 热门商圈推荐 -->
      <view
        class="hot-districts bg-white mx-20rpx mb-20rpx rounded-xl shadow-sm"
      >
        <view class="px-30rpx py-30rpx">
          <view class="flex justify-between items-center mb-30rpx">
            <text class="text-30rpx font-bold">热门商圈</text>
            <view class="flex items-center" @tap="navigateToDistrictList">
              <text class="text-24rpx color-grey">查看更多</text>
              <text
                class="i-carbon-chevron-right text-20rpx color-grey ml-6rpx"
              ></text>
            </view>
          </view>
          <scroll-view scroll-x class="districts-scroll">
            <view class="districts-list flex">
              <view
                v-for="(district, index) in hotDistricts"
                :key="index"
                class="district-item mr-20rpx"
                @tap="filterByDistrict(district)"
              >
                <image
                  :src="district.image"
                  class="district-image rounded-lg mb-10rpx"
                  mode="aspectFill"
                ></image>
                <text class="text-24rpx font-bold line-clamp-1">{{
                  district.name
                }}</text>
                <text class="text-22rpx color-grey mt-4rpx">{{
                  district.avgPrice
                }}</text>
                <view class="district-tags mt-8rpx">
                  <text
                    v-for="(tag, tagIndex) in district.tags.slice(0, 2)"
                    :key="tagIndex"
                    class="tag-mini mr-8rpx"
                    >{{ tag }}</text
                  >
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>

      <view class="house-list px-20rpx">
        <HouseItem
          v-for="item in houseList"
          :key="item.id"
          :house="item"
          type="commercial"
        />
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import HouseFilterBar from "@/components/house/HouseFilterBar.vue";
import HouseItem from "@/components/house/HouseItem.vue";

// z-paging组件引用
const paging = ref(null);

// 房源列表数据
const houseList = ref([]);

// 区域选项
const areaOptions = [
  { label: "不限", value: "" },
  { label: "朝阳区", value: "chaoyang" },
  { label: "海淀区", value: "haidian" },
  { label: "东城区", value: "dongcheng" },
  { label: "西城区", value: "xicheng" },
  { label: "丰台区", value: "fengtai" },
  { label: "石景山区", value: "shijingshan" },
  { label: "通州区", value: "tongzhou" },
  { label: "昌平区", value: "changping" },
  { label: "大兴区", value: "daxing" },
  { label: "顺义区", value: "shunyi" },
  { label: "房山区", value: "fangshan" },
];

// 商业地产类型选项
const propertyTypeOptions = [
  { label: "不限", value: "" },
  { label: "商铺", value: "shop" },
  { label: "写字楼", value: "office" },
  { label: "厂房", value: "factory" },
  { label: "仓库", value: "warehouse" },
];

// 价格选项 - 区分出售和出租
const priceOptions = [
  { label: "不限", value: "" },
  { label: "5000元/月以下", value: "rent,0,5000" },
  { label: "5000-10000元/月", value: "rent,5000,10000" },
  { label: "10000-20000元/月", value: "rent,10000,20000" },
  { label: "20000元/月以上", value: "rent,20000," },
  { label: "100万以下", value: "sale,0,100" },
  { label: "100-300万", value: "sale,100,300" },
  { label: "300-500万", value: "sale,300,500" },
  { label: "500-1000万", value: "sale,500,1000" },
  { label: "1000万以上", value: "sale,1000," },
];

// 排序选项
const sortOptions = [
  { label: "默认排序", value: "default" },
  { label: "价格从低到高", value: "price_asc" },
  { label: "价格从高到低", value: "price_desc" },
  { label: "面积从大到小", value: "area_desc" },
  { label: "最新发布", value: "time_desc" },
];

// 活跃的筛选条件
const activeFilters = reactive({
  area: "",
  propertyType: "",
  price: "",
  dealType: "rent", // 默认为租赁，可选值：rent(租赁)、sale(出售)
  sort: "",
  more: {},
});

// 商业地产特色功能
const commercialFeatures = [
  {
    name: "临街商铺",
    desc: "人流旺盛",
    icon: "i-carbon-store",
    bgClass: "from-red-400 to-red-600",
    filter: { propertyType: "商铺", more: { streetFront: true } },
  },
  {
    name: "写字楼",
    desc: "商务办公",
    icon: "i-carbon-building",
    bgClass: "from-blue-400 to-blue-600",
    filter: { propertyType: "写字楼" },
  },
  {
    name: "厂房",
    desc: "生产制造",
    icon: "i-carbon-factory",
    bgClass: "from-green-400 to-green-600",
    filter: { propertyType: "厂房" },
  },
  {
    name: "出售",
    desc: "产权清晰",
    icon: "i-carbon-money",
    bgClass: "from-yellow-400 to-yellow-600",
    filter: { dealType: "sale" },
  },
];

// 热门商圈数据
const hotDistricts = [
  {
    id: "district1",
    name: "CBD商务区",
    image: "https://picsum.photos/seed/district1/200/150",
    avgPrice: "租金8-15元/㎡/天",
    tags: ["甲级写字楼", "交通便利", "配套完善"],
  },
  {
    id: "district2",
    name: "金融街",
    image: "https://picsum.photos/seed/district2/200/150",
    avgPrice: "租金10-20元/㎡/天",
    tags: ["金融中心", "高端商务", "地标建筑"],
  },
  {
    id: "district3",
    name: "中关村",
    image: "https://picsum.photos/seed/district3/200/150",
    avgPrice: "租金6-12元/㎡/天",
    tags: ["科技园区", "创新创业", "人才聚集"],
  },
  {
    id: "district4",
    name: "望京SOHO",
    image: "https://picsum.photos/seed/district4/200/150",
    avgPrice: "租金7-14元/㎡/天",
    tags: ["现代商务", "国际化", "年轻活力"],
  },
  {
    id: "district5",
    name: "亦庄开发区",
    image: "https://picsum.photos/seed/district5/200/150",
    avgPrice: "租金4-8元/㎡/天",
    tags: ["产业园区", "制造基地", "政策优惠"],
  },
];

// 应用筛选条件
const applyFilter = (filter: any) => {
  // 合并筛选条件
  Object.keys(filter).forEach((key) => {
    if (key === "more") {
      activeFilters.more = { ...activeFilters.more, ...filter.more };
    } else {
      activeFilters[key] = filter[key];
    }
  });

  // 重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// 处理筛选条件变更
const handleFilterChange = (filters: any) => {
  // 更新筛选条件
  Object.assign(activeFilters, filters);

  // 重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// 获取商业地产列表
const queryHouseList = (pageNo: number, pageSize: number) => {
  // 模拟异步获取数据
  setTimeout(() => {
    // 构建查询参数
    const params = {
      page: pageNo,
      pageSize: pageSize,
      area: activeFilters.area || "",
      propertyType: activeFilters.propertyType || "",
      price: activeFilters.price || "",
      dealType: activeFilters.dealType || "rent",
      sort: activeFilters.sort || "default",
      ...activeFilters.more,
    };

    // 模拟API返回数据
    const list = Array.from({ length: pageSize }, (_, i) => {
      const index = (pageNo - 1) * pageSize + i;
      const isSale = activeFilters.dealType === "sale";

      // 获取随机属性类型
      const type =
        activeFilters.propertyType ||
        ["商铺", "写字楼", "厂房", "仓库"][Math.floor(Math.random() * 4)];

      // 根据类型设置不同的标题模板
      let titlePrefix;
      switch (type) {
        case "商铺":
          titlePrefix = ["临街旺铺", "购物中心商铺", "社区底商", "商业街店面"][
            Math.floor(Math.random() * 4)
          ];
          break;
        case "写字楼":
          titlePrefix = ["甲级写字楼", "商务中心", "创意园区", "独栋办公"][
            Math.floor(Math.random() * 4)
          ];
          break;
        case "厂房":
          titlePrefix = ["标准厂房", "钢构厂房", "生产车间", "工业园区"][
            Math.floor(Math.random() * 4)
          ];
          break;
        case "仓库":
          titlePrefix = ["标准仓库", "物流中心", "冷库", "保税仓"][
            Math.floor(Math.random() * 4)
          ];
          break;
        default:
          titlePrefix = "商业地产";
      }

      return {
        id: `comm${index}`,
        title: `${activeFilters.area || "北京"} ${titlePrefix}`,
        image: `https://picsum.photos/seed/comm${index}/300/200`,
        type: type,
        area: `${Math.floor(Math.random() * 500) + 50}`,
        location: `${activeFilters.area || "北京"}${
          ["商圈", "开发区", "产业园", "CBD"][Math.floor(Math.random() * 4)]
        }`,
        price: isSale
          ? Math.floor(Math.random() * 500) + 100
          : Math.floor(Math.random() * 20000) + 3000,
        priceType: isSale ? "sale" : "rent",
        unitPrice: isSale
          ? `${Math.floor(Math.random() * 20000) + 10000}元/㎡`
          : null,
        tags: [
          "临街",
          "独立产权",
          "精装修",
          "近地铁",
          "高层",
          "配套齐全",
          "可分割",
          "双证齐全",
        ].slice(0, Math.floor(Math.random() * 4) + 1),
        extraInfo: `${Math.floor(Math.random() * 100) + 1}人看过`,
      };
    });

    // 更新z-paging组件数据
    if (paging.value) {
      paging.value.complete(list);
    }
  }, 500);
};

// 导航到搜索页
const navigateToSearch = () => {
  uni.navigateTo({
    url: "/pages/house/search?type=commercial",
  });
};

// 导航到地图找房
const navigateToMap = () => {
  uni.navigateTo({
    url: "/pages/house/map?type=commercial",
  });
};

// 导航到商圈列表
const navigateToDistrictList = () => {
  uni.navigateTo({
    url: "/pages/house/district/list",
  });
};

// 按商圈筛选
const filterByDistrict = (district: any) => {
  applyFilter({ area: district.name });
};

// 页面加载
onLoad((options: any) => {
  // 解析URL参数
  if (options) {
    if (options.area) {
      const area = areaOptions.find((a) => a.value === options.area);
      if (area) {
        activeFilters.area = area.label;
      }
    }

    if (options.type) {
      const propertyType = propertyTypeOptions.find(
        (p) => p.value === options.type
      );
      if (propertyType) {
        activeFilters.propertyType = propertyType.label;
      } else {
        // 直接使用传入的类型名称
        activeFilters.propertyType = options.type;
      }
    }

    if (options.dealType) {
      activeFilters.dealType = options.dealType;
    }
  }

  // 首次加载数据
  if (paging.value) {
    paging.value.reload();
  }
});
</script>

<style lang="scss" scoped>
.commercial-house-list {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.search-header {
  position: sticky;
  top: 0;
  z-index: 100;
}

.feature-card {
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.districts-scroll {
  white-space: nowrap;
}

.districts-list {
  display: inline-flex;
  padding: 10rpx 0;
}

.district-item {
  flex-shrink: 0;
  width: 160rpx;

  &:active {
    opacity: 0.7;
  }
}

.district-image {
  width: 160rpx;
  height: 120rpx;
}

.tag-mini {
  padding: 4rpx 8rpx;
  background-color: #f0f0f0;
  color: #666;
  border-radius: 8rpx;
  font-size: 20rpx;
}

.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.color-grey {
  color: #666;
}

.house-list {
  padding-bottom: 30rpx;
}
</style>
