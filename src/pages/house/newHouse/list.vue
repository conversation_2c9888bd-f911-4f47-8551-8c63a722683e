<template>
  <view class="new-house-list">
    <!-- 房源列表 -->
    <z-paging
      ref="paging"
      v-model="houseList"
      @query="queryHouseList"
      :refresher-enabled="true"
      :show-refresher-update-time="true"
      :auto-scroll-to-top-when-reload="true"
      :empty-view-text="'暂无符合条件的楼盘'"
    >
      <!-- 顶部搜索栏 -->
      <view class="search-header bg-white p-20rpx flex items-center">
        <view class="city-selector flex items-center mr-20rpx">
          <text class="text-32rpx font-bold">北京</text>
          <text class="i-carbon-chevron-down text-24rpx ml-6rpx"></text>
        </view>
        <view
          class="search-box flex-1 flex items-center bg-gray-100 rounded-full px-30rpx py-16rpx"
          @tap="navigateToSearch"
        >
          <text class="i-carbon-search text-28rpx color-grey mr-10rpx"></text>
          <text class="color-grey text-28rpx">搜索楼盘名称</text>
        </view>
        <view class="map-icon ml-20rpx p-10rpx" @tap="navigateToMap">
          <text class="i-carbon-map text-36rpx"></text>
        </view>
      </view>

      <!-- 新房特色功能区 -->
      <view
        class="new-house-features bg-white mx-20rpx my-20rpx rounded-xl shadow-sm"
      >
        <view class="px-30rpx py-30rpx">
          <text class="text-30rpx font-bold mb-30rpx block">新房专区</text>
          <view class="grid grid-cols-5 gap-y-30rpx">
            <view
              v-for="(item, index) in newHouseFeatures"
              :key="index"
              class="feature-card flex flex-col items-center"
              @tap="applyFilter(item.filter)"
            >
              <view
                :class="[
                  item.bgClass,
                  'feature-icon-circle mb-12rpx flex items-center justify-center',
                ]"
              >
                <text :class="[item.icon, 'text-white']"></text>
              </view>
              <text class="text-22rpx color-info text-center">{{
                item.name
              }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 更多功能入口 -->
      <view
        class="more-features bg-white mx-20rpx mb-20rpx rounded-xl shadow-sm"
      >
        <view class="px-30rpx py-30rpx">
          <view class="flex justify-between items-center mb-20rpx">
            <text class="text-30rpx font-bold">热门楼盘</text>
            <view class="flex items-center" @tap="navigateToHotList">
              <text class="text-26rpx color-grey">查看全部</text>
              <text
                class="i-carbon-chevron-right text-20rpx color-grey ml-6rpx"
              ></text>
            </view>
          </view>
          <scroll-view scroll-x class="projects-scroll">
            <view class="projects-list flex">
              <view
                v-for="(project, index) in hotProjects"
                :key="index"
                class="project-item mr-20rpx"
                @tap="navigateToProjectDetail(project.id)"
              >
                <image
                  :src="project.image"
                  class="project-image rounded-lg mb-10rpx"
                  mode="aspectFill"
                ></image>
                <text class="text-24rpx font-bold line-clamp-1">{{
                  project.name
                }}</text>
                <text class="text-22rpx color-grey mt-4rpx">{{
                  project.price
                }}</text>
                <view class="project-tags mt-8rpx">
                  <text
                    v-for="(tag, tagIndex) in project.tags.slice(0, 2)"
                    :key="tagIndex"
                    class="tag-mini mr-8rpx"
                    >{{ tag }}</text
                  >
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>

      <!-- 筛选条件栏 - 使用tui-sticky实现吸顶效果 -->
      <tui-sticky :scrollTop="scrollTop" stickyHeight="400rpx">
        <template v-slot:header>
          <HouseFilterBar
            :showHouseType="false"
            :showPrice="true"
            :priceText="'均价'"
            :areaOptions="areaOptions"
            :priceOptions="priceOptions"
            :sortOptions="sortOptions"
            :initialFilters="activeFilters"
            @filter-change="handleFilterChange"
          />
        </template>
      </tui-sticky>

      <view class="house-list px-20rpx">
        <HouseItem
          v-for="(item, index) in houseList"
          :key="item.id"
          :house="item"
          type="new"
        />
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import HouseFilterBar from "@/components/house/HouseFilterBar.vue";
import HouseItem from "@/components/house/HouseItem.vue";
import tuiSticky from "@/components/thorui/tui-sticky/tui-sticky.vue";

// z-paging组件引用
const paging = ref(null);

// 记录滚动位置
const scrollTop = ref(0);

onPageScroll((e) => {
  console.log(e);
  scrollTop.value = e.scrollTop;
});

// 房源列表数据
const houseList = ref([]);

// 区域选项
const areaOptions = [
  { label: "不限", value: "" },
  { label: "朝阳区", value: "chaoyang" },
  { label: "海淀区", value: "haidian" },
  { label: "东城区", value: "dongcheng" },
  { label: "西城区", value: "xicheng" },
  { label: "丰台区", value: "fengtai" },
  { label: "石景山区", value: "shijingshan" },
  { label: "通州区", value: "tongzhou" },
  { label: "昌平区", value: "changping" },
  { label: "大兴区", value: "daxing" },
  { label: "顺义区", value: "shunyi" },
  { label: "房山区", value: "fangshan" },
];

// 价格选项
const priceOptions = [
  { label: "不限", value: "" },
  { label: "2万以下", value: "0,20000" },
  { label: "2-3万", value: "20000,30000" },
  { label: "3-4万", value: "30000,40000" },
  { label: "4-5万", value: "40000,50000" },
  { label: "5-6万", value: "50000,60000" },
  { label: "6-8万", value: "60000,80000" },
  { label: "8万以上", value: "80000," },
];

// 排序选项
const sortOptions = [
  { label: "默认排序", value: "default" },
  { label: "均价从低到高", value: "price_asc" },
  { label: "均价从高到低", value: "price_desc" },
  { label: "开盘时间最新", value: "time_desc" },
  { label: "热门程度优先", value: "hot" },
];

// 筛选条件
const activeFilters = reactive({
  area: "",
  price: "",
  sort: "",
  more: {},
});

// 新房特色功能
const newHouseFeatures = [
  {
    name: "近期开盘",
    desc: "新盘上市",
    icon: "i-carbon-calendar text-32rpx",
    bgClass: "bg-blue-500",
    filter: { tag: "recent" },
  },
  {
    name: "品牌开发商",
    desc: "实力保障",
    icon: "i-carbon-building text-32rpx",
    bgClass: "bg-green-500",
    filter: { tag: "brand" },
  },
  {
    name: "低总价",
    desc: "首付友好",
    icon: "i-carbon-money text-32rpx",
    bgClass: "bg-orange-500",
    filter: { tag: "lowprice" },
  },
  {
    name: "精装修",
    desc: "拎包入住",
    icon: "i-carbon-home text-32rpx",
    bgClass: "bg-purple-500",
    filter: { tag: "decoration" },
  },
  {
    name: "VR看房",
    desc: "身临其境",
    icon: "i-carbon-view text-32rpx",
    bgClass: "bg-red-500",
    filter: { tag: "vr" },
  },
  {
    name: "地铁沿线",
    desc: "出行便利",
    icon: "i-carbon-train text-32rpx",
    bgClass: "bg-cyan-500",
    filter: { tag: "metro" },
  },
  {
    name: "学区房",
    desc: "名校学区",
    icon: "i-carbon-education text-32rpx",
    bgClass: "bg-yellow-500",
    filter: { tag: "school" },
  },
  {
    name: "湖景房",
    desc: "生态环境",
    icon: "i-carbon-sail text-32rpx",
    bgColor: "bg-teal-500",
    filter: { tag: "lake" },
  },
  {
    name: "特价房",
    desc: "限时优惠",
    icon: "i-carbon-tag text-32rpx",
    bgColor: "bg-pink-500",
    filter: { tag: "special" },
  },
  {
    name: "全部分类",
    desc: "更多筛选",
    icon: "i-carbon-category text-32rpx",
    bgColor: "bg-gray-500",
    filter: { tag: "all" },
  },
];

// 热门楼盘数据
const hotProjects = [
  {
    id: "project1",
    name: "碧桂园·翡翠湾",
    image: "https://picsum.photos/seed/project1/200/150",
    price: "均价2.8万/㎡",
    tags: ["近地铁", "学区房", "品牌开发商"],
  },
  {
    id: "project2",
    name: "万科·金域华府",
    image: "https://picsum.photos/seed/project2/200/150",
    price: "均价3.2万/㎡",
    tags: ["精装修", "公园旁", "低总价"],
  },
  {
    id: "project3",
    name: "恒大·御景半岛",
    image: "https://picsum.photos/seed/project3/200/150",
    price: "均价2.5万/㎡",
    tags: ["江景房", "品牌开发商", "近商圈"],
  },
  {
    id: "project4",
    name: "保利·云禧",
    image: "https://picsum.photos/seed/project4/200/150",
    price: "均价3.5万/㎡",
    tags: ["高端社区", "精装修", "学区房"],
  },
  {
    id: "project5",
    name: "融创·壹号院",
    image: "https://picsum.photos/seed/project5/200/150",
    price: "均价4.2万/㎡",
    tags: ["豪宅", "品牌开发商", "稀缺户型"],
  },
];
// 页面加载
onLoad((options: any) => {
  // 解析URL参数
  if (options) {
    if (options.area) {
      const area = areaOptions.find((a) => a.value === options.area);
      if (area) {
        activeFilters.area = area.label;
      }
    }

    if (options.filter) {
      applySpecialFilter(options.filter);
    }
  }

  // 首次加载数据
  if (paging.value) {
    paging.value.reload();
  }
});

// 处理特殊筛选参数
const applySpecialFilter = (filterType: string) => {
  switch (filterType) {
    case "recent":
      activeFilters.more = { ...activeFilters.more, isRecent: true };
      break;
    case "brand":
      activeFilters.more = { ...activeFilters.more, isBrand: true };
      break;
    case "lowprice":
      activeFilters.price = "3万以下";
      break;
    case "decoration":
      activeFilters.more = { ...activeFilters.more, isDecoration: true };
      break;
    case "vr":
      activeFilters.more = { ...activeFilters.more, hasVR: true };
      break;
    case "metro":
      activeFilters.more = { ...activeFilters.more, nearMetro: true };
      break;
    case "school":
      activeFilters.more = { ...activeFilters.more, isSchoolDistrict: true };
      break;
    case "lake":
      activeFilters.more = { ...activeFilters.more, isLakeView: true };
      break;
    case "special":
      activeFilters.more = { ...activeFilters.more, isSpecialPrice: true };
      break;
  }
};

// 应用筛选条件
const applyFilter = (filter: any) => {
  // 合并筛选条件
  Object.assign(activeFilters, filter);

  // 重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// 处理筛选条件变更
const handleFilterChange = (filters: any) => {
  // 更新筛选条件
  Object.assign(activeFilters, filters);

  // 重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// 获取房源列表数据
const queryHouseList = (pageNo: number, pageSize: number) => {
  // 这里使用setTimeout模拟异步API请求
  setTimeout(() => {
    // 构建查询参数
    const params = {
      page: pageNo,
      pageSize: pageSize,
      area: activeFilters.area || "",
      price: activeFilters.price || "",
      sort: activeFilters.sort || "default",
      ...activeFilters.more,
    };

    // 模拟API返回数据
    const list = Array.from({ length: pageSize }, (_, i) => {
      const index = (pageNo - 1) * pageSize + i;
      return {
        id: `new${index}`,
        name: `${activeFilters.area || "北京"}碧桂园·蓝湾${
          Math.floor(Math.random() * 10) + 1
        }号院`,
        image: `https://picsum.photos/seed/newhouse${index}/300/200`,
        location: `${activeFilters.area || "北京"} · ${
          ["滨江花园", "中央公园", "科技新城", "商务区"][
            Math.floor(Math.random() * 4)
          ]
        }`,
        price: Math.floor(Math.random() * 30000) + 20000,
        tags: [
          "近地铁",
          "学区房",
          "公园旁",
          "精装修",
          "品牌开发商",
          "低总价",
        ].slice(0, Math.floor(Math.random() * 4) + 1),
        status: ["在售", "即将开盘", "售罄"][Math.floor(Math.random() * 3)],
        openTime: `${Math.floor(Math.random() * 12) + 1}月${
          Math.floor(Math.random() * 28) + 1
        }日`,
        extraInfo: `关注${Math.floor(Math.random() * 1000) + 100}人`,
      };
    });

    // 更新z-paging组件数据
    if (paging.value) {
      paging.value.complete(list);
    }
  }, 500);
};

// 导航到指定页面
const navigateTo = (path: string) => {
  uni.navigateTo({
    url: path,
  });
};

// 导航到搜索页
const navigateToSearch = () => {
  uni.navigateTo({
    url: "/pages/house/search?type=new",
  });
};

// 导航到地图找房
const navigateToMap = () => {
  uni.navigateTo({
    url: "/pages/house/map?type=new",
  });
};

// 导航到热门楼盘列表
const navigateToHotList = () => {
  uni.navigateTo({
    url: "/pages/house/newHouse/hot",
  });
};

// 导航到楼盘详情
const navigateToProjectDetail = (id: string) => {
  uni.navigateTo({
    url: `/pages/house/newHouse/detail?id=${id}`,
  });
};
</script>

<style lang="scss" scoped>
.new-house-list {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.search-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}

.feature-card {
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.92);
  }
}

.feature-icon-circle {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
}

.projects-scroll {
  white-space: nowrap;
}

.projects-list {
  display: inline-flex;
  padding: 10rpx 0;
}

.project-item {
  flex-shrink: 0;
  width: 160rpx;

  &:active {
    opacity: 0.7;
  }
}

.project-image {
  width: 160rpx;
  height: 120rpx;
}

.tag-mini {
  padding: 4rpx 8rpx;
  background-color: #f0f0f0;
  color: #666;
  border-radius: 8rpx;
  font-size: 20rpx;
}

.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.color-grey {
  color: #666;
}

.color-info {
  color: #333;
}

.house-list {
  padding-bottom: 30rpx;
}
</style>
