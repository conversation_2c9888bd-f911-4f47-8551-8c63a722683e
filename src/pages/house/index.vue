<template>
  <view class="house-index">
    <!-- 顶部搜索栏 - 优化设计 -->
    <view
      class="search-header bg-white px-20rpx py-20rpx flex items-center shadow-sm"
    >
      <view class="city-selector flex items-center mr-20rpx" @tap="selectCity">
        <text class="i-carbon-location text-24rpx color-primary mr-6rpx"></text>
        <text class="text-30rpx font-bold">{{ currentCity }}</text>
        <text
          class="i-carbon-chevron-down text-20rpx ml-6rpx color-grey"
        ></text>
      </view>
      <view
        class="search-box flex-1 flex items-center bg-gray-50 rounded-full px-24rpx py-18rpx border border-gray-200"
        @tap="navigateToSearch"
      >
        <text class="i-carbon-search text-26rpx color-primary mr-12rpx"></text>
        <text class="color-grey text-26rpx">搜索小区、地标、学校</text>
      </view>
      <view
        class="message-icon ml-20rpx p-12rpx relative"
        @tap="navigateToMessage"
      >
        <text class="i-carbon-notification text-32rpx color-grey"></text>
        <view v-if="hasMessage" class="message-dot"></view>
      </view>
    </view>

    <scroll-view scroll-y class="house-content">
      <!-- 广告轮播图 -->
      <view class="banner-container px-20rpx py-20rpx">
        <swiper
          class="banner-swiper"
          :indicator-dots="true"
          :autoplay="true"
          :interval="3000"
          :duration="500"
          indicator-color="rgba(255, 255, 255, 0.6)"
          indicator-active-color="$primary"
        >
          <swiper-item
            v-for="(item, index) in bannerList"
            :key="index"
            @tap="navigateTo(item.link)"
          >
            <image
              :src="item.image"
              mode="aspectFill"
              class="banner-image rounded-lg"
            />
          </swiper-item>
        </swiper>
      </view>

      <!-- 核心功能区域 - 九宫格布局 -->
      <view
        class="core-modules bg-white mx-20rpx mb-20rpx rounded-xl shadow-sm"
      >
        <view class="px-20rpx py-24rpx">
          <view class="grid grid-cols-5 gap-y-30rpx">
            <view
              v-for="(item, index) in coreModules"
              :key="index"
              class="module-item flex flex-col items-center"
              @tap="navigateTo(item.path)"
            >
              <view
                :class="[
                  item.bgColor,
                  'module-icon-box rounded-full mb-12rpx flex items-center justify-center shadow-sm',
                ]"
              >
                <text :class="[item.icon, 'text-white']"></text>
              </view>
              <text class="text-22rpx color-info text-center">{{
                item.name
              }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 快捷服务区 - 九宫格第二行 -->
      <view
        class="quick-services bg-white mx-20rpx mb-20rpx rounded-xl shadow-sm"
      >
        <view class="px-20rpx py-24rpx">
          <view class="section-header mb-20rpx">
            <text class="text-30rpx font-bold">特色服务</text>
          </view>
          <view class="grid grid-cols-5 gap-y-30rpx">
            <view
              v-for="(item, index) in quickServices"
              :key="index"
              class="service-item flex flex-col items-center"
              @tap="navigateTo(item.path)"
            >
              <view
                :class="[
                  item.bgColor,
                  'service-icon-box rounded-full mb-12rpx flex items-center justify-center shadow-sm',
                ]"
              >
                <text :class="[item.icon, 'text-white']"></text>
              </view>
              <text class="text-22rpx color-info text-center">{{
                item.name
              }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 行情播报 -->
      <view class="market-info bg-white mx-20rpx mb-20rpx rounded-xl shadow-sm">
        <view class="section-header border-bottom px-20rpx py-20rpx">
          <text class="text-30rpx font-bold">行情播报</text>
        </view>

        <view class="market-data flex px-20rpx py-30rpx">
          <!-- 挂牌均价 -->
          <view class="market-item flex-1 border-right">
            <view class="text-26rpx color-grey">挂牌均价</view>
            <view class="text-40rpx font-bold color-red mt-10rpx"
              >56,827<text class="text-26rpx">元/㎡</text></view
            >
            <view class="text-24rpx color-grey mt-6rpx">与上月持平</view>
          </view>

          <!-- 新增挂牌 -->
          <view class="market-item flex-1 text-center">
            <view class="text-26rpx color-grey">新增挂牌</view>
            <view class="text-40rpx font-bold mt-10rpx"
              >845<text class="text-26rpx">套</text></view
            >
            <view class="text-24rpx color-green mt-6rpx">比上日 ↑2.8%</view>
          </view>
        </view>
      </view>

      <!-- 推荐好房 -->
      <view
        class="recommend-house bg-white mx-20rpx mb-20rpx rounded-xl shadow-sm"
      >
        <view
          class="section-header flex justify-between items-center px-20rpx py-20rpx border-bottom"
        >
          <text class="text-30rpx font-bold">推荐好房</text>
          <view
            class="flex items-center"
            @tap="navigateTo('/pages/house/recommend/list')"
          >
            <text class="text-26rpx color-grey">更多好房</text>
            <text class="i-carbon-chevron-right text-24rpx color-grey"></text>
          </view>
        </view>

        <view class="house-list px-20rpx py-20rpx">
          <view
            v-for="(item, index) in recommendHouses"
            :key="index"
            class="house-card mb-20rpx"
            @tap="navigateToDetail(item.type, item.id)"
          >
            <view class="flex">
              <image
                :src="item.image"
                class="house-image rounded-lg"
                mode="aspectFill"
              ></image>
              <view class="house-info ml-20rpx flex-1">
                <view class="text-32rpx font-bold line-clamp-1">{{
                  item.title
                }}</view>
                <view class="text-28rpx color-grey mt-10rpx">{{
                  item.info
                }}</view>
                <view class="flex flex-wrap mt-10rpx">
                  <text
                    v-for="(tag, tIndex) in item.tags"
                    :key="tIndex"
                    class="tag-item mr-10rpx mb-10rpx"
                    >{{ tag }}</text
                  >
                </view>
                <view
                  class="price-line flex justify-between items-center mt-10rpx"
                >
                  <text class="text-32rpx color-red font-bold">{{
                    item.price
                  }}</text>
                  <text class="text-24rpx color-grey" v-if="item.unitPrice">{{
                    item.unitPrice
                  }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 房产知识 -->
      <view
        class="house-knowledge bg-white mx-20rpx mb-20rpx rounded-xl shadow-sm"
      >
        <view
          class="section-header flex justify-between items-center px-20rpx py-20rpx border-bottom"
        >
          <text class="text-30rpx font-bold">房产知识</text>
          <view
            class="flex items-center"
            @tap="navigateTo('/pages/house/guide/list')"
          >
            <text class="text-26rpx color-grey">查看全部</text>
            <text class="i-carbon-chevron-right text-24rpx color-grey"></text>
          </view>
        </view>

        <view class="guides-list px-20rpx pb-20rpx">
          <view
            v-for="(guide, index) in rentGuides"
            :key="index"
            class="guide-item flex p-20rpx mb-20rpx bg-white rounded-lg shadow-sm"
            @tap="navigateTo(`/pages/house/guide/detail?id=${guide.id}`)"
          >
            <image
              :src="guide.image"
              mode="aspectFill"
              class="guide-image rounded-lg"
            />
            <view class="guide-info flex-1 ml-20rpx">
              <text class="guide-title text-30rpx font-bold line-clamp-2">{{
                guide.title
              }}</text>
              <view class="flex justify-between items-center mt-20rpx">
                <text class="author text-24rpx color-grey">{{
                  guide.author
                }}</text>
                <text class="view-count text-24rpx color-grey"
                  >{{ guide.viewCount }}阅读</text
                >
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 发布房源按钮 -->
    <view class="publish-btn-container">
      <button
        class="publish-btn bg-primary text-white"
        @tap="navigateTo('/pages/house/publish/select')"
      >
        <text class="i-carbon-add mr-10rpx"></text>
        <text>发布房源</text>
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { onLoad } from "@dcloudio/uni-app";

// 是否有未读消息
const hasMessage = ref(true);

// 当前城市
const currentCity = ref("丰宁县");

// 轮播图数据
const bannerList = [
  {
    image: "https://picsum.photos/seed/house1/800/300",
    link: "/pages/house/newHouse/list?filter=hot",
  },
  {
    image: "https://picsum.photos/seed/house2/800/300",
    link: "/pages/house/secondHouse/list?filter=school",
  },
  {
    image: "https://picsum.photos/seed/house3/800/300",
    link: "/pages/house/rent/list?rentType=整租",
  },
];

// 核心模块 - 九宫格布局，5列
const coreModules = [
  {
    name: "二手房",
    icon: "i-carbon-home text-36rpx",
    bgColor: "bg-blue-500",
    path: "/pages/house/secondHouse/list",
  },
  {
    name: "新房",
    icon: "i-carbon-building text-36rpx",
    bgColor: "bg-orange-500",
    path: "/pages/house/newHouse/list",
  },
  {
    name: "租房",
    icon: "i-carbon-key text-36rpx",
    bgColor: "bg-green-500",
    path: "/pages/house/rent/list",
  },
  {
    name: "商铺办公",
    icon: "i-carbon-store text-36rpx",
    bgColor: "bg-purple-500",
    path: "/pages/house/commercial/list",
  },
  {
    name: "地图找房",
    icon: "i-carbon-map text-36rpx",
    bgColor: "bg-cyan-500",
    path: "/pages/house/map",
  },
  {
    name: "降价房源",
    icon: "i-carbon-cut text-36rpx",
    bgColor: "bg-red-500",
    path: "/pages/house/secondHouse/list?filter=price_cut",
  },
  {
    name: "学区房",
    icon: "i-carbon-education text-36rpx",
    bgColor: "bg-yellow-500",
    path: "/pages/house/secondHouse/list?filter=school",
  },
  {
    name: "品牌公寓",
    icon: "i-carbon-building-insights text-36rpx",
    bgColor: "bg-teal-500",
    path: "/pages/house/rent/list?type=公寓",
  },
  {
    name: "我的收藏",
    icon: "i-carbon-favorite text-36rpx",
    bgColor: "bg-pink-500",
    path: "/pages/house/favorite",
  },
  {
    name: "我的看房",
    icon: "i-carbon-calendar text-36rpx",
    bgColor: "bg-indigo-500",
    path: "/pages/house/viewings",
  },
];

// 快捷服务区 - 九宫格第二行
const quickServices = [
  {
    name: "房贷计算",
    icon: "i-carbon-calculator text-32rpx",
    bgColor: "bg-blue-500",
    path: "/pages/house/calculator",
  },
  {
    name: "房价走势",
    icon: "i-carbon-chart-line text-32rpx",
    bgColor: "bg-green-500",
    path: "/pages/house/price-trend",
  },
  {
    name: "购房资格",
    icon: "i-carbon-document text-32rpx",
    bgColor: "bg-orange-500",
    path: "/pages/house/qualification",
  },
  {
    name: "注册登记",
    icon: "i-carbon-user-admin text-32rpx",
    bgColor: "bg-purple-500",
    path: "/pages/house/registration",
  },
  {
    name: "买房攻略",
    icon: "i-carbon-book text-32rpx",
    bgColor: "bg-red-500",
    path: "/pages/house/guide/list?type=buy",
  },
  {
    name: "卖房流程",
    icon: "i-carbon-document-signature text-32rpx",
    bgColor: "bg-yellow-500",
    path: "/pages/house/guide/list?type=sell",
  },
  {
    name: "房产百科",
    icon: "i-carbon-help text-32rpx",
    bgColor: "bg-teal-500",
    path: "/pages/house/faq",
  },
  {
    name: "税费计算",
    icon: "i-carbon-calculation text-32rpx",
    bgColor: "bg-cyan-500",
    path: "/pages/house/tax-calculator",
  },
  {
    name: "咨询专家",
    icon: "i-carbon-chat text-32rpx",
    bgColor: "bg-pink-500",
    path: "/pages/house/expert",
  },
  {
    name: "优惠活动",
    icon: "i-carbon-gift text-32rpx",
    bgColor: "bg-indigo-500",
    path: "/pages/house/promotion",
  },
];

// 推荐房源数据
const recommendHouses = ref([
  {
    id: `house1`,
    type: "second",
    title: `阳光花园精装三室两厅`,
    image: `https://picsum.photos/seed/house1/300/200`,
    info: `3室2厅 | 108㎡ | 南北通透`,
    tags: ["满二年", "近地铁", "精装修"],
    price: `550万`,
    unitPrice: `51,000元/㎡`,
  },
  {
    id: `house2`,
    type: "rent",
    title: `金桥国际公寓精装大两居`,
    image: `https://picsum.photos/seed/house2/300/200`,
    info: `2室1厅 | 85㎡ | 整租`,
    tags: ["拎包入住", "近商圈", "家电齐全"],
    price: `3500元/月`,
  },
  {
    id: `house3`,
    type: "new",
    title: `碧桂园·蓝湾南岸`,
    image: `https://picsum.photos/seed/house3/300/200`,
    info: `城南区·滨江花园`,
    tags: ["近地铁", "公园旁", "精装修"],
    price: `6500元/㎡起`,
  },
]);

// 房产知识
const rentGuides = [
  {
    id: "1",
    image: "https://picsum.photos/seed/guide1/250/180",
    title: "新手租房必看：如何选到合适的房子？",
    author: "租房专家",
    viewCount: "25.8万",
  },
  {
    id: "2",
    image: "https://picsum.photos/seed/guide2/250/180",
    title: "房屋租赁合同怎么签？这些条款必须注意",
    author: "法律顾问",
    viewCount: "18.3万",
  },
];

// 选择城市
const selectCity = () => {
  uni.showActionSheet({
    itemList: ["丰宁县", "承德市", "北京市"],
    success: (res) => {
      const cities = ["丰宁县", "承德市", "北京市"];
      currentCity.value = cities[res.tapIndex];
    },
  });
};

// 导航到搜索页
const navigateToSearch = () => {
  uni.navigateTo({
    url: "/pages/house/search",
  });
};

// 导航到消息页面
const navigateToMessage = () => {
  uni.navigateTo({
    url: "/pages/message/list",
  });
};

// 导航到详情页
const navigateToDetail = (type: string, id: string) => {
  uni.navigateTo({
    url: `/pages/house/${type}/detail?id=${id}`,
  });
};

// 通用导航方法
const navigateTo = (path: string) => {
  uni.navigateTo({
    url: path,
  });
};

// 页面加载
onLoad(() => {
  // 加载数据，实际应该从API获取
  console.log("找房首页加载完成");
});
</script>

<style lang="scss" scoped>
.house-index {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.search-header {
  flex-shrink: 0;
  width: 100%;
  z-index: 100;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.house-content {
  flex: 1;
  height: 0;
}

.message-dot {
  position: absolute;
  top: 6rpx;
  right: 6rpx;
  width: 16rpx;
  height: 16rpx;
  background-color: $primary;
  border-radius: 50%;
}

.banner-container {
  background-color: #fff;
}

.banner-swiper {
  height: 280rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 280rpx;
}

.module-icon-box,
.service-icon-box {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.module-item,
.service-item {
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.92);
  }
}

.border-bottom {
  border-bottom: 1px solid #f0f0f0;
}

.border-right {
  border-right: 1px solid #f0f0f0;
}

.color-red {
  color: #fa5741;
}

.color-green {
  color: #39b54a;
}

.house-image {
  width: 220rpx;
  height: 160rpx;
}

.tag-item {
  padding: 4rpx 12rpx;
  background-color: #f6f6f6;
  color: #666;
  border-radius: 4rpx;
  font-size: 22rpx;
}

.guide-image {
  width: 200rpx;
  height: 140rpx;
}

.color-grey {
  color: #666;
}

.color-info {
  color: #333;
}

.publish-btn-container {
  position: fixed;
  bottom: 30rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  z-index: 90;
}

.publish-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 109, 0, 0.3);
}
</style>

<!--
重构说明:

1. 优化了找房首页结构:
   - 移除了重复的标签页和列表，解决了"两个列表"的问题
   - 设计成主流房产APP形式的入口页面，展示四大核心功能(二手房、新房、租房、商铺办公)
   - 添加广告轮播图，增加视觉吸引力
   - 加入行情播报、推荐房源、房产知识等模块，丰富内容

2. 代码优化:
   - 将onMounted替换为onLoad，符合uni-app最佳实践
   - 数据结构优化，减少冗余代码
   - 简化组件逻辑，提高性能

3. 性能优化:
   - 减少不必要的渲染
   - 静态数据与动态数据分离

4. 增强可维护性:
   - 抽离可复用组件(HouseFilterBar, HouseItem)
   - 统一房源数据结构，方便维护

参考贝壳找房、自如等行业标杆，打造了更符合用户习惯的界面布局，提升了用户导航体验。
-->
