<template>
  <view class="rent-house-list">
    <!-- 顶部搜索栏 -->
    <view class="search-header bg-white p-20rpx flex items-center">
      <view class="city-selector flex items-center mr-20rpx">
        <text class="text-32rpx font-bold">北京</text>
        <text class="i-carbon-chevron-down text-24rpx ml-6rpx"></text>
      </view>
      <view
        class="search-box flex-1 flex items-center bg-gray-100 rounded-full px-30rpx py-16rpx"
        @tap="navigateToSearch"
      >
        <text class="i-carbon-search text-28rpx color-grey mr-10rpx"></text>
        <text class="color-grey text-28rpx">小区名称/地址</text>
      </view>
      <view class="map-icon ml-20rpx p-10rpx" @tap="navigateToMap">
        <text class="i-carbon-map text-36rpx"></text>
      </view>
    </view>

    <!-- 租房特色功能区 -->
    <view class="rent-features bg-white mx-20rpx my-20rpx rounded-xl shadow-sm">
      <view class="px-30rpx py-30rpx">
        <text class="text-30rpx font-bold mb-30rpx block">租房专区</text>
        <view class="grid grid-cols-5 gap-y-30rpx">
          <view
            v-for="(item, index) in rentFeatures"
            :key="index"
            class="feature-card flex flex-col items-center"
            @tap="applyFilter(item.filter)"
          >
            <view
              :class="[
                item.bgClass,
                'feature-icon-circle mb-12rpx flex items-center justify-center',
              ]"
            >
              <text :class="[item.icon, 'text-white']"></text>
            </view>
            <text class="text-22rpx color-info text-center">{{
              item.name
            }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 品牌公寓推荐 -->
    <view
      class="brand-apartments bg-white mx-20rpx mb-20rpx rounded-xl shadow-sm"
    >
      <view class="px-30rpx py-30rpx">
        <view class="flex justify-between items-center mb-30rpx">
          <text class="text-30rpx font-bold">品牌公寓</text>
          <view class="flex items-center" @tap="navigateToBrandList">
            <text class="text-24rpx color-grey">更多品牌</text>
            <text
              class="i-carbon-chevron-right text-20rpx color-grey ml-6rpx"
            ></text>
          </view>
        </view>
        <scroll-view scroll-x class="brands-scroll">
          <view class="brands-list flex">
            <view
              v-for="(brand, index) in brandApartments"
              :key="index"
              class="brand-item mr-20rpx"
              @tap="filterByBrand(brand)"
            >
              <image
                :src="brand.logo"
                class="brand-logo rounded-lg mb-10rpx"
                mode="aspectFill"
              ></image>
              <text class="text-24rpx text-center block">{{ brand.name }}</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 筛选条件栏 - 使用tui-sticky实现吸顶效果 -->
    <tui-sticky :scrollTop="scrollTop" stickyHeight="90rpx">
      <HouseFilterBar
        :showHouseType="true"
        :showRentType="true"
        :areaOptions="areaOptions"
        :priceOptions="priceOptions"
        :houseTypeOptions="houseTypeOptions"
        :rentTypeOptions="rentTypeOptions"
        :sortOptions="sortOptions"
        :initialFilters="activeFilters"
        :priceText="'租金'"
        :rentTypeText="'方式'"
        @filter-change="handleFilterChange"
      />
    </tui-sticky>

    <!-- 房源列表 -->
    <z-paging
      ref="paging"
      :fixed="false"
      v-model="houseList"
      @query="queryHouseList"
      :refresher-enabled="true"
      :show-refresher-update-time="true"
      :auto-scroll-to-top-when-reload="true"
      :empty-view-text="'暂无符合条件的房源'"
      @scroll="onScroll"
    >
      <view class="house-list px-20rpx">
        <HouseItem
          v-for="item in houseList"
          :key="item.id"
          :house="item"
          type="rent"
        />
      </view>
    </z-paging>

    <!-- 筛选弹出层 -->
    <uni-popup ref="areaFilterPopup" type="bottom">
      <view class="filter-popup bg-white p-30rpx">
        <view class="popup-header flex justify-between mb-30rpx">
          <text class="text-32rpx font-bold">选择区域</text>
          <text class="i-carbon-close text-32rpx" @tap="hideAllPopups"></text>
        </view>
        <scroll-view scroll-y style="max-height: 600rpx">
          <view class="area-list">
            <view
              v-for="(area, index) in areaOptions"
              :key="area.value"
              class="area-item py-20rpx"
              :class="{ 'active-filter': activeFilters.area === area.label }"
              @tap="selectArea(area)"
            >
              <text class="text-28rpx">{{ area.label }}</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </uni-popup>

    <uni-popup ref="rentTypeFilterPopup" type="bottom">
      <view class="filter-popup bg-white p-30rpx">
        <view class="popup-header flex justify-between mb-30rpx">
          <text class="text-32rpx font-bold">选择方式</text>
          <text class="i-carbon-close text-32rpx" @tap="hideAllPopups"></text>
        </view>
        <view class="rent-type-list grid grid-cols-3 gap-20rpx mb-30rpx">
          <view
            v-for="(type, index) in rentTypeOptions"
            :key="type.value"
            class="rent-type-item py-20rpx text-center rounded-lg"
            :class="{
              'active-filter-item': activeFilters.rentType === type.label,
            }"
            @tap="selectRentType(type)"
          >
            <text class="text-28rpx">{{ type.label }}</text>
          </view>
        </view>
      </view>
    </uni-popup>

    <uni-popup ref="priceFilterPopup" type="bottom">
      <view class="filter-popup bg-white p-30rpx">
        <view class="popup-header flex justify-between mb-30rpx">
          <text class="text-32rpx font-bold">选择租金</text>
          <text class="i-carbon-close text-32rpx" @tap="hideAllPopups"></text>
        </view>
        <scroll-view scroll-y style="max-height: 600rpx">
          <view class="price-list">
            <view
              v-for="(price, index) in priceOptions"
              :key="price.value"
              class="price-item py-20rpx"
              :class="{ 'active-filter': activeFilters.price === price.label }"
              @tap="selectPrice(price)"
            >
              <text class="text-28rpx">{{ price.label }}</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </uni-popup>

    <uni-popup ref="houseTypeFilterPopup" type="bottom">
      <view class="filter-popup bg-white p-30rpx">
        <view class="popup-header flex justify-between mb-30rpx">
          <text class="text-32rpx font-bold">选择户型</text>
          <text class="i-carbon-close text-32rpx" @tap="hideAllPopups"></text>
        </view>
        <view class="house-type-list grid grid-cols-3 gap-20rpx">
          <view
            v-for="(type, index) in houseTypeOptions"
            :key="type.value"
            class="house-type-item py-20rpx text-center rounded-lg"
            :class="{
              'active-filter-item': activeFilters.houseType === type.label,
            }"
            @tap="selectHouseType(type)"
          >
            <text class="text-28rpx">{{ type.label }}</text>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import HouseFilterBar from "@/components/house/HouseFilterBar.vue";
import HouseItem from "@/components/house/HouseItem.vue";
import tuiSticky from "@/components/thorui/tui-sticky/tui-sticky.vue";

// z-paging组件引用
const paging = ref(null);

// 记录滚动位置
const scrollTop = ref(0);

// 房源列表数据
const houseList = ref([]);

// 筛选弹出层引用
const areaFilterPopup = ref(null);
const priceFilterPopup = ref(null);
const houseTypeFilterPopup = ref(null);
const rentTypeFilterPopup = ref(null);

// 区域选项
const areaOptions = [
  { label: "不限", value: "" },
  { label: "朝阳区", value: "chaoyang" },
  { label: "海淀区", value: "haidian" },
  { label: "东城区", value: "dongcheng" },
  { label: "西城区", value: "xicheng" },
  { label: "丰台区", value: "fengtai" },
  { label: "石景山区", value: "shijingshan" },
  { label: "通州区", value: "tongzhou" },
  { label: "昌平区", value: "changping" },
  { label: "大兴区", value: "daxing" },
  { label: "顺义区", value: "shunyi" },
  { label: "房山区", value: "fangshan" },
];

// 租金选项
const priceOptions = [
  { label: "不限", value: "" },
  { label: "1000元以下", value: "0,1000" },
  { label: "1000-2000元", value: "1000,2000" },
  { label: "2000-3000元", value: "2000,3000" },
  { label: "3000-4000元", value: "3000,4000" },
  { label: "4000-5000元", value: "4000,5000" },
  { label: "5000-8000元", value: "5000,8000" },
  { label: "8000元以上", value: "8000," },
];

// 户型选项
const houseTypeOptions = [
  { label: "不限", value: "" },
  { label: "一室", value: "1" },
  { label: "二室", value: "2" },
  { label: "三室", value: "3" },
  { label: "四室+", value: "4+" },
];

// 租房方式选项
const rentTypeOptions = [
  { label: "不限", value: "" },
  { label: "整租", value: "entire" },
  { label: "合租", value: "shared" },
  { label: "公寓", value: "apartment" },
];

// 排序选项
const sortOptions = [
  { label: "默认排序", value: "default" },
  { label: "租金从低到高", value: "price_asc" },
  { label: "租金从高到低", value: "price_desc" },
  { label: "面积从大到小", value: "area_desc" },
  { label: "最新发布", value: "time_desc" },
];

// 筛选条件
const activeFilters = reactive({
  area: "",
  price: "",
  houseType: "",
  rentType: "",
  sort: "",
  more: {} as any,
});

// 页面滚动事件
const onScroll = (e: any) => {
  scrollTop.value = e.detail.scrollTop;
};

// 租房特色功能
const rentFeatures = [
  {
    name: "整租",
    icon: "i-carbon-home text-32rpx",
    bgClass: "bg-blue-500",
    filter: { rentType: "整租" },
  },
  {
    name: "合租",
    icon: "i-carbon-collaborate text-32rpx",
    bgClass: "bg-green-500",
    filter: { rentType: "合租" },
  },
  {
    name: "品牌公寓",
    icon: "i-carbon-building-insights text-32rpx",
    bgClass: "bg-orange-500",
    filter: { rentType: "公寓" },
  },
  {
    name: "近地铁",
    icon: "i-carbon-train text-32rpx",
    bgClass: "bg-purple-500",
    filter: { tag: "near_subway" },
  },
  {
    name: "拎包入住",
    icon: "i-carbon-shopping-bag text-32rpx",
    bgClass: "bg-red-500",
    filter: { tag: "ready_to_move" },
  },
  {
    name: "精装修",
    icon: "i-carbon-home-theater text-32rpx",
    bgClass: "bg-pink-500",
    filter: { tag: "fine_decoration" },
  },
  {
    name: "独卫",
    icon: "i-carbon-shower text-32rpx",
    bgClass: "bg-cyan-500",
    filter: { tag: "private_bathroom" },
  },
  {
    name: "押一付一",
    icon: "i-carbon-purchase text-32rpx",
    bgClass: "bg-yellow-500",
    filter: { tag: "low_deposit" },
  },
  {
    name: "带阳台",
    icon: "i-carbon-sun text-32rpx",
    bgClass: "bg-teal-500",
    filter: { tag: "has_balcony" },
  },
  {
    name: "全部筛选",
    icon: "i-carbon-filter text-32rpx",
    bgClass: "bg-gray-500",
    filter: { tag: "all" },
  },
];

// 品牌公寓数据
const brandApartments = [
  {
    id: "brand1",
    name: "自如",
    logo: "https://picsum.photos/seed/brand1/100/100",
  },
  {
    id: "brand2",
    name: "蛋壳公寓",
    logo: "https://picsum.photos/seed/brand2/100/100",
  },
  {
    id: "brand3",
    name: "链家",
    logo: "https://picsum.photos/seed/brand3/100/100",
  },
  {
    id: "brand4",
    name: "相寓",
    logo: "https://picsum.photos/seed/brand4/100/100",
  },
  {
    id: "brand5",
    name: "青客公寓",
    logo: "https://picsum.photos/seed/brand5/100/100",
  },
  {
    id: "brand6",
    name: "魔方公寓",
    logo: "https://picsum.photos/seed/brand6/100/100",
  },
];

// 页面加载
onLoad((options: any) => {
  // 解析URL参数
  if (options) {
    if (options.area) {
      const area = areaOptions.find((a) => a.value === options.area);
      if (area) {
        activeFilters.area = area.label;
      }
    }

    if (options.rentType) {
      const rentType = rentTypeOptions.find(
        (t) => t.value === options.rentType
      );
      if (rentType) {
        activeFilters.rentType = rentType.label;
      }
    }

    if (options.filter) {
      applySpecialFilter(options.filter);
    }
  }

  // 首次加载数据
  if (paging.value) {
    paging.value.reload();
  }
});

// 处理特殊筛选参数
const applySpecialFilter = (filterType: string) => {
  switch (filterType) {
    case "near_subway":
      activeFilters.more = { ...activeFilters.more, isNearSubway: true };
      break;
    case "ready_to_move":
      activeFilters.more = { ...activeFilters.more, isReadyToMove: true };
      break;
    case "fine_decoration":
      activeFilters.more = { ...activeFilters.more, isFineDecoration: true };
      break;
    case "private_bathroom":
      activeFilters.more = { ...activeFilters.more, hasPrivateBathroom: true };
      break;
    case "low_deposit":
      activeFilters.more = { ...activeFilters.more, isLowDeposit: true };
      break;
    case "has_balcony":
      activeFilters.more = { ...activeFilters.more, hasBalcony: true };
      break;
  }
};

// 应用筛选条件
const applyFilter = (filter: any) => {
  // 合并筛选条件
  Object.assign(activeFilters, filter);

  // 重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// 根据品牌筛选房源
const filterByBrand = (brand: any) => {
  activeFilters.more = { ...activeFilters.more, brandId: brand.id };
  if (paging.value) {
    paging.value.reload();
  }
};

// 处理筛选条件变更
const handleFilterChange = (filters: any) => {
  // 更新筛选条件
  Object.assign(activeFilters, filters);

  // 重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// 获取房源列表数据
const queryHouseList = (pageNo: number, pageSize: number) => {
  // 这里使用setTimeout模拟异步API请求
  setTimeout(() => {
    // 构建查询参数
    const params = {
      page: pageNo,
      pageSize: pageSize,
      area: activeFilters.area || "",
      price: activeFilters.price || "",
      houseType: activeFilters.houseType || "",
      rentType: activeFilters.rentType || "",
      sort: activeFilters.sort || "default",
      ...activeFilters.more,
    };

    // 模拟API返回数据
    const list = Array.from({ length: pageSize }, (_, i) => {
      const index = (pageNo - 1) * pageSize + i;
      const rooms = Math.floor(Math.random() * 2) + 1;
      const area = Math.floor(Math.random() * 60) + 30;
      const price = Math.floor(Math.random() * 3000) + 2000;
      const rentTypes = ["整租", "合租", "公寓"];
      const rentType =
        activeFilters.rentType ||
        rentTypes[Math.floor(Math.random() * rentTypes.length)];
      const isNearSubway =
        activeFilters.more?.isNearSubway || Math.random() > 0.7;
      const isFineDecoration =
        activeFilters.more?.isFineDecoration || Math.random() > 0.8;

      return {
        id: `rent${index}`,
        title: `${activeFilters.area || "北京"}${
          brandApartments[Math.floor(Math.random() * brandApartments.length)]
            .name
        } ${rentType}${rooms}室${rooms === 2 ? "1厅" : ""}`,
        image: `https://picsum.photos/seed/rent${index}/300/200`,
        tags: [
          rentType,
          ...(isNearSubway ? ["近地铁"] : []),
          ...(isFineDecoration ? ["精装修"] : []),
          "拎包入住",
          "有电梯",
          "独卫",
          "带阳台",
        ].slice(0, Math.floor(Math.random() * 4) + 1),
        layout: `${rooms}室${rooms === 2 ? "1厅" : ""}1卫`,
        area: area,
        floor: `${Math.floor(Math.random() * 20) + 1}/${
          Math.floor(Math.random() * 10) + 10
        }层`,
        price: price,
        location: `${activeFilters.area || "北京"} · ${
          ["望京", "中关村", "朝外", "双井"][Math.floor(Math.random() * 4)]
        }`,
        distance: `距离${
          ["13号线", "6号线", "1号线", "10号线"][Math.floor(Math.random() * 4)]
        }${Math.floor(Math.random() * 800) + 200}米`,
      };
    });

    // 更新z-paging组件数据
    if (paging.value) {
      paging.value.complete(list);
    }
  }, 500);
};

// 导航到搜索页
const navigateToSearch = () => {
  uni.navigateTo({
    url: "/pages/house/search?type=rent",
  });
};

// 导航到地图找房
const navigateToMap = () => {
  uni.navigateTo({
    url: "/pages/house/map?type=rent",
  });
};

// 导航到品牌列表
const navigateToBrandList = () => {
  uni.navigateTo({
    url: "/pages/house/brand/list",
  });
};

// 选择区域
const selectArea = (area: any) => {
  activeFilters.area = area.label;
  hideAllPopups();
  if (paging.value) {
    paging.value.reload();
  }
};

// 选择租金
const selectPrice = (price: any) => {
  activeFilters.price = price.label;
  hideAllPopups();
  if (paging.value) {
    paging.value.reload();
  }
};

// 选择户型
const selectHouseType = (type: any) => {
  activeFilters.houseType = type.label;
  hideAllPopups();
  if (paging.value) {
    paging.value.reload();
  }
};

// 选择租房方式
const selectRentType = (type: any) => {
  activeFilters.rentType = type.label;
  hideAllPopups();
  if (paging.value) {
    paging.value.reload();
  }
};

// 隐藏所有弹出层
const hideAllPopups = () => {
  areaFilterPopup.value?.close();
  priceFilterPopup.value?.close();
  houseTypeFilterPopup.value?.close();
  rentTypeFilterPopup.value?.close();
};
</script>

<style lang="scss" scoped>
.rent-house-list {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.search-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}

.feature-card {
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.92);
  }
}

.feature-icon-circle {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
}

.brands-scroll {
  white-space: nowrap;
}

.brands-list {
  display: inline-flex;
  padding: 10rpx 0;
}

.brand-item {
  flex-shrink: 0;
  text-align: center;

  &:active {
    opacity: 0.7;
  }
}

.brand-logo {
  width: 100rpx;
  height: 100rpx;
}

.filter-popup {
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
}

.active-filter {
  color: $primary;
  font-weight: bold;
}

.active-filter-item {
  background-color: rgba(255, 109, 0, 0.1);
  color: $primary;
  font-weight: bold;
}

.house-type-item,
.rent-type-item {
  background-color: #f8f8f8;
}

.color-grey {
  color: #666;
}

.color-info {
  color: #333;
}
</style>
