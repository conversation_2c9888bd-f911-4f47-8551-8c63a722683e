<template>
  <view class="second-house-list">
    <!-- 顶部搜索栏 -->
    <view class="search-header bg-white p-20rpx flex items-center">
      <view class="city-selector flex items-center mr-20rpx">
        <text class="text-32rpx font-bold">北京</text>
        <text class="i-carbon-chevron-down text-24rpx ml-6rpx"></text>
      </view>
      <view
        class="search-box flex-1 flex items-center bg-gray-100 rounded-full px-30rpx py-16rpx"
        @tap="navigateToSearch"
      >
        <text class="i-carbon-search text-28rpx color-grey mr-10rpx"></text>
        <text class="color-grey text-28rpx">找小区、找位置</text>
      </view>
      <view class="map-icon ml-20rpx p-10rpx" @tap="navigateToMap">
        <text class="i-carbon-map text-36rpx"></text>
      </view>
    </view>
    <!-- 房源列表 -->
    <z-paging
      ref="paging"
      v-model="houseList"
      @query="queryHouseList"
      :refresher-enabled="true"
      :show-refresher-update-time="true"
      :auto-scroll-to-top-when-reload="true"
      :empty-view-text="'暂无符合条件的房源'"
    >
      <!-- 二手房特色功能区 -->
      <view
        class="second-house-features bg-white mx-20rpx my-20rpx rounded-xl shadow-sm"
      >
        <view class="px-30rpx py-30rpx">
          <text class="text-30rpx font-bold mb-30rpx block">二手房专区</text>
          <view class="grid grid-cols-5 gap-y-30rpx">
            <view
              v-for="(item, index) in secondHouseFeatures"
              :key="index"
              class="feature-card flex flex-col items-center"
              @tap="applyFilter(item.filter)"
            >
              <view
                :class="[
                  item.bgClass,
                  'feature-icon-circle mb-12rpx flex items-center justify-center',
                ]"
              >
                <text :class="[item.icon, 'text-white']"></text>
              </view>
              <text class="text-22rpx color-info text-center">{{
                item.name
              }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 优质小区推荐 -->
      <view
        class="quality-communities bg-white mx-20rpx mb-20rpx rounded-xl shadow-sm"
      >
        <view class="px-30rpx py-30rpx">
          <view class="flex justify-between items-center mb-30rpx">
            <text class="text-30rpx font-bold">优质小区</text>
            <view class="flex items-center" @tap="navigateToCommunityList">
              <text class="text-24rpx color-grey">查看更多</text>
              <text
                class="i-carbon-chevron-right text-20rpx color-grey ml-6rpx"
              ></text>
            </view>
          </view>
          <scroll-view scroll-x class="communities-scroll">
            <view class="communities-list flex">
              <view
                v-for="(community, index) in qualityCommunities"
                :key="index"
                class="community-item mr-20rpx"
                @tap="filterByCommunity(community)"
              >
                <image
                  :src="community.image"
                  class="community-image rounded-lg mb-10rpx"
                  mode="aspectFill"
                ></image>
                <text class="text-24rpx font-bold line-clamp-1">{{
                  community.name
                }}</text>
                <text class="text-22rpx color-grey mt-4rpx">{{
                  community.avgPrice
                }}</text>
                <view class="community-tags mt-8rpx">
                  <text
                    v-for="(tag, tagIndex) in community.tags.slice(0, 2)"
                    :key="tagIndex"
                    class="tag-mini mr-8rpx"
                    >{{ tag }}</text
                  >
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>

      <!-- 筛选条件栏 - 使用tui-sticky实现吸顶效果 -->
      <tui-sticky :scrollTop="scrollTop" stickyHeight="90rpx">
        <HouseFilterBar
          :showHouseType="true"
          :areaOptions="areaOptions"
          :priceOptions="priceOptions"
          :houseTypeOptions="houseTypeOptions"
          :sortOptions="sortOptions"
          :initialFilters="activeFilters"
          @filter-change="handleFilterChange"
        />
      </tui-sticky>

      <view class="house-list px-20rpx">
        <HouseItem
          v-for="(item, index) in houseList"
          :key="item.id"
          :house="item"
          type="second"
        />
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import HouseFilterBar from "@/components/house/HouseFilterBar.vue";
import HouseItem from "@/components/house/HouseItem.vue";
import tuiSticky from "@/components/thorui/tui-sticky/tui-sticky.vue";

// z-paging组件引用
const paging = ref(null);

// 记录滚动位置
const scrollTop = ref(0);

// 房源列表数据
const houseList = ref([]);

// 区域选项
const areaOptions = [
  { label: "不限", value: "" },
  { label: "朝阳区", value: "chaoyang" },
  { label: "海淀区", value: "haidian" },
  { label: "东城区", value: "dongcheng" },
  { label: "西城区", value: "xicheng" },
  { label: "丰台区", value: "fengtai" },
  { label: "石景山区", value: "shijingshan" },
  { label: "通州区", value: "tongzhou" },
  { label: "昌平区", value: "changping" },
  { label: "大兴区", value: "daxing" },
  { label: "顺义区", value: "shunyi" },
  { label: "房山区", value: "fangshan" },
];

// 价格选项
const priceOptions = [
  { label: "不限", value: "" },
  { label: "200万以下", value: "0,200" },
  { label: "200-300万", value: "200,300" },
  { label: "300-500万", value: "300,500" },
  { label: "500-800万", value: "500,800" },
  { label: "800-1000万", value: "800,1000" },
  { label: "1000-1500万", value: "1000,1500" },
  { label: "1500-2000万", value: "1500,2000" },
  { label: "2000万以上", value: "2000," },
];

// 户型选项
const houseTypeOptions = [
  { label: "不限", value: "" },
  { label: "一室", value: "1" },
  { label: "二室", value: "2" },
  { label: "三室", value: "3" },
  { label: "四室", value: "4" },
  { label: "五室+", value: "5+" },
];

// 排序选项
const sortOptions = [
  { label: "默认排序", value: "default" },
  { label: "价格从低到高", value: "price_asc" },
  { label: "价格从高到低", value: "price_desc" },
  { label: "面积从大到小", value: "area_desc" },
  { label: "最新发布", value: "time_desc" },
];

// 筛选条件
const activeFilters = reactive({
  area: "",
  price: "",
  houseType: "",
  sort: "",
  more: {} as any,
});

// 二手房特色功能
const secondHouseFeatures = [
  {
    name: "学区房",
    icon: "i-carbon-education text-32rpx",
    bgClass: "bg-blue-500",
    filter: { tag: "school" },
  },
  {
    name: "降价房",
    icon: "i-carbon-cut text-32rpx",
    bgClass: "bg-red-500",
    filter: { tag: "price_cut" },
  },
  {
    name: "满五唯一",
    icon: "i-carbon-certificate text-32rpx",
    bgClass: "bg-green-500",
    filter: { tag: "five_only" },
  },
  {
    name: "近地铁",
    icon: "i-carbon-train text-32rpx",
    bgClass: "bg-purple-500",
    filter: { tag: "near_subway" },
  },
  {
    name: "南北通透",
    icon: "i-carbon-wind-stream text-32rpx",
    bgClass: "bg-cyan-500",
    filter: { tag: "good_layout" },
  },
  {
    name: "精装修",
    icon: "i-carbon-home text-32rpx",
    bgClass: "bg-orange-500",
    filter: { tag: "fine_decoration" },
  },
  {
    name: "一楼花园",
    icon: "i-carbon-tree text-32rpx",
    bgClass: "bg-yellow-500",
    filter: { tag: "garden" },
  },
  {
    name: "顶层复式",
    icon: "i-carbon-stairs text-32rpx",
    bgClass: "bg-teal-500",
    filter: { tag: "duplex" },
  },
  {
    name: "落户社区",
    icon: "i-carbon-user-admin text-32rpx",
    bgClass: "bg-pink-500",
    filter: { tag: "hukou" },
  },
  {
    name: "全部筛选",
    icon: "i-carbon-filter text-32rpx",
    bgClass: "bg-gray-500",
    filter: { tag: "all" },
  },
];

// 页面滚动事件
const onScroll = (e: any) => {
  scrollTop.value = e.detail.scrollTop;
};

// 优质小区数据
const qualityCommunities = [
  {
    id: "community1",
    name: "万科城市花园",
    image: "https://picsum.photos/seed/community1/200/150",
    avgPrice: "均价52,500元/㎡",
    tags: ["精装修", "近地铁", "学区房"],
  },
  {
    id: "community2",
    name: "保利香槟国际",
    image: "https://picsum.photos/seed/community2/200/150",
    avgPrice: "均价48,300元/㎡",
    tags: ["近公园", "配套齐全", "品牌物业"],
  },
  {
    id: "community3",
    name: "绿城百合公寓",
    image: "https://picsum.photos/seed/community3/200/150",
    avgPrice: "均价45,800元/㎡",
    tags: ["南北通透", "交通便利", "成熟社区"],
  },
  {
    id: "community4",
    name: "金地国际花园",
    image: "https://picsum.photos/seed/community4/200/150",
    avgPrice: "均价56,200元/㎡",
    tags: ["学区房", "品质小区", "绿化好"],
  },
  {
    id: "community5",
    name: "龙湖春江郦城",
    image: "https://picsum.photos/seed/community5/200/150",
    avgPrice: "均价51,500元/㎡",
    tags: ["高端物业", "近商圈", "安静宜居"],
  },
];

// 页面加载
onLoad((options: any) => {
  // 解析URL参数
  if (options) {
    if (options.area) {
      const area = areaOptions.find((a) => a.value === options.area);
      if (area) {
        activeFilters.area = area.label;
      }
    }

    if (options.filter) {
      applySpecialFilter(options.filter);
    }
  }

  // 首次加载数据
  if (paging.value) {
    paging.value.reload();
  }
});

// 处理特殊筛选参数
const applySpecialFilter = (filterType: string) => {
  switch (filterType) {
    case "school":
      activeFilters.more = { ...activeFilters.more, isSchool: true };
      break;
    case "price_cut":
      activeFilters.more = { ...activeFilters.more, isPriceCut: true };
      break;
    case "five_only":
      activeFilters.more = { ...activeFilters.more, isFiveOnly: true };
      break;
    case "near_subway":
      activeFilters.more = { ...activeFilters.more, isNearSubway: true };
      break;
    case "good_layout":
      activeFilters.more = { ...activeFilters.more, isGoodLayout: true };
      break;
    case "fine_decoration":
      activeFilters.more = { ...activeFilters.more, isFineDecoration: true };
      break;
    case "garden":
      activeFilters.more = { ...activeFilters.more, isGarden: true };
      break;
    case "duplex":
      activeFilters.more = { ...activeFilters.more, isDuplex: true };
      break;
    case "hukou":
      activeFilters.more = { ...activeFilters.more, supportHukou: true };
      break;
  }
};

// 应用筛选条件
const applyFilter = (filter: any) => {
  // 合并筛选条件
  Object.assign(activeFilters, filter);

  // 重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// 根据小区筛选房源
const filterByCommunity = (community: any) => {
  activeFilters.more = { ...activeFilters.more, communityId: community.id };
  if (paging.value) {
    paging.value.reload();
  }
};

// 处理筛选条件变更
const handleFilterChange = (filters: any) => {
  // 更新筛选条件
  Object.assign(activeFilters, filters);

  // 重新加载数据
  if (paging.value) {
    paging.value.reload();
  }
};

// 获取房源列表数据
const queryHouseList = (pageNo: number, pageSize: number) => {
  // 这里使用setTimeout模拟异步API请求
  setTimeout(() => {
    // 构建查询参数
    const params = {
      page: pageNo,
      pageSize: pageSize,
      area: activeFilters.area || "",
      price: activeFilters.price || "",
      houseType: activeFilters.houseType || "",
      sort: activeFilters.sort || "default",
      ...activeFilters.more,
    };

    // 模拟API返回数据
    const list = Array.from({ length: pageSize }, (_, i) => {
      const index = (pageNo - 1) * pageSize + i;
      const rooms = Math.floor(Math.random() * 3) + 1;
      const area = Math.floor(Math.random() * 80) + 60;
      const price = Math.floor(Math.random() * 300) + 200;
      const isSchool = activeFilters.more?.isSchool || Math.random() > 0.7;
      const isPriceCut = activeFilters.more?.isPriceCut || Math.random() > 0.8;

      return {
        id: `second${index}`,
        title: `${activeFilters.area || "北京"}${
          qualityCommunities[
            Math.floor(Math.random() * qualityCommunities.length)
          ].name
        } ${rooms}室${rooms > 1 ? "2" : "1"}厅`,
        image: `https://picsum.photos/seed/secondhouse${index}/300/200`,
        tags: [
          ...(isSchool ? ["学区房"] : []),
          ...(isPriceCut ? ["降价房"] : []),
          "南北通透",
          "精装修",
          "核心地段",
          "满五年",
          "近地铁",
        ].slice(0, Math.floor(Math.random() * 3) + 1),
        layout: `${rooms}室${rooms > 1 ? "2" : "1"}厅`,
        area: area,
        floor: `${Math.floor(Math.random() * 30) + 1}/${
          Math.floor(Math.random() * 10) + 25
        }层`,
        direction: ["朝南", "南北通透", "东南", "西南"][
          Math.floor(Math.random() * 4)
        ],
        decoration: ["精装修", "简装修", "毛坯房"][
          Math.floor(Math.random() * 3)
        ],
        price: price,
        unitPrice: Math.floor((price * 10000) / area),
        location: `${activeFilters.area || "北京市"} · ${
          ["朝阳区", "海淀区", "东城区", "西城区"][
            Math.floor(Math.random() * 4)
          ]
        }`,
      };
    });

    // 更新z-paging组件数据
    if (paging.value) {
      paging.value.complete(list);
    }
  }, 500);
};

// 导航到搜索页
const navigateToSearch = () => {
  uni.navigateTo({
    url: "/pages/house/search?type=second",
  });
};

// 导航到地图找房
const navigateToMap = () => {
  uni.navigateTo({
    url: "/pages/house/map?type=second",
  });
};

// 导航到小区列表
const navigateToCommunityList = () => {
  uni.navigateTo({
    url: "/pages/house/community/list",
  });
};
</script>

<style lang="scss" scoped>
.second-house-list {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.search-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #fff;
}

.feature-card {
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.92);
  }
}

.feature-icon-circle {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
}

.communities-scroll {
  white-space: nowrap;
}

.communities-list {
  display: inline-flex;
  padding: 10rpx 0;
}

.community-item {
  flex-shrink: 0;
  width: 160rpx;

  &:active {
    opacity: 0.7;
  }
}

.community-image {
  width: 160rpx;
  height: 120rpx;
}

.tag-mini {
  padding: 4rpx 8rpx;
  background-color: #f0f0f0;
  color: #666;
  border-radius: 8rpx;
  font-size: 20rpx;
}

.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.color-grey {
  color: #666;
}

.color-info {
  color: #333;
}

.house-list {
  padding-bottom: 30rpx;
}
</style>
